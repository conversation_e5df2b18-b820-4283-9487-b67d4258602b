import { commonLocale } from './common';
import type { Locale } from '../interface';

const locale: Locale = {
  ...commonLocale,
  locale: 'it_IT',
  today: 'Oggi',
  now: '<PERSON>esso',
  backToToday: 'Torna ad oggi',
  ok: 'OK',
  clear: 'Can<PERSON>a',
  week: '<PERSON><PERSON><PERSON>',
  month: 'Mese',
  year: 'Ann<PERSON>',
  timeSelect: "Seleziona l'ora",
  dateSelect: 'Seleziona la data',
  monthSelect: 'Seleziona il mese',
  yearSelect: "Seleziona l'anno",
  decadeSelect: 'Seleziona il decennio',

  previousMonth: 'Il mese scorso (PageUp)',
  nextMonth: 'Il prossimo mese (PageDown)',
  previousYear: "L'anno scorso (Control + sinistra)",
  nextYear: "L'anno prossimo (Control + destra)",
  previousDecade: 'Ultimo decennio',
  nextDecade: 'Prossimo decennio',
  previousCentury: 'Se<PERSON>lo precedente',
  nextCentury: 'Prossimo secolo',
  shortWeekDays: ['Dom', 'Lun', 'Mar', 'Mer', '<PERSON><PERSON>', 'Ven', 'Sab'],
  shortMonths: ['Gen', 'Feb', 'Mar', 'Apr', 'Mag', 'Giu', 'Lug', 'Ago', 'Set', 'Ott', 'Nov', 'Dic'],
};

export default locale;
