import { commonLocale } from './common';
import type { Locale } from '../interface';

const locale: Locale = {
  ...commonLocale,
  locale: 'id_ID',
  today: 'Hari ini',
  now: 'Sekarang',
  backToToday: 'Kembali ke hari ini',
  ok: 'Baik',
  clear: 'Be<PERSON><PERSON>',
  week: '<PERSON><PERSON>',
  month: 'Bulan',
  year: '<PERSON>hun',
  timeSelect: 'pilih waktu',
  dateSelect: 'pilih tanggal',
  weekSelect: 'Pilih satu minggu',
  monthSelect: 'Pilih satu bulan',
  yearSelect: 'Pilih satu tahun',
  decadeSelect: 'Pilih satu dekade',

  previousMonth: '<PERSON><PERSON><PERSON> sebel<PERSON> (PageUp)',
  nextMonth: '<PERSON><PERSON><PERSON> selan<PERSON> (PageDown)',
  previousYear: '<PERSON>hun lalu (Control + kiri)',
  nextYear: '<PERSON>hun selanjutnya (Kontrol + kanan)',
  previousDecade: 'Dekade terakhir',
  nextDecade: 'Dekade berikutnya',
  previousCentury: 'Abad terakhir',
  nextCentury: '<PERSON>bad berikutnya',
};

export default locale;
