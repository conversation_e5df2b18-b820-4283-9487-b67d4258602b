import { commonLocale } from './common';
import type { Locale } from '../interface';

const locale: Locale = {
  ...commonLocale,
  locale: 'ms_MY',
  today: 'Hari ini',
  now: 'Sekarang',
  backToToday: 'Ke<PERSON>li ke hari ini',
  ok: 'OK',
  timeSelect: '<PERSON>lih masa',
  dateSelect: 'Pilih tarikh',
  weekSelect: 'Pilih minggu',
  clear: 'Padam',
  week: '<PERSON>gu',
  month: 'Bulan',
  year: '<PERSON>hun',
  previousMonth: 'Bulan lepas',
  nextMonth: 'Bulan depan',
  monthSelect: '<PERSON>lih bulan',
  yearSelect: '<PERSON>lih tahun',
  decadeSelect: 'Pilih dekad',

  previousYear: '<PERSON>hun lepas (Ctrl+left)',
  nextYear: 'Tahun depan (Ctrl+right)',
  previousDecade: 'Dekad lepas',
  nextDecade: 'Dekad depan',
  previousCentury: 'Abad lepas',
  nextCentury: 'Abad depan',

  monthBeforeYear: false,
};

export default locale;
