import { commonLocale } from './common';
import type { Locale } from '../interface';

const locale: Locale = {
  ...commonLocale,
  locale: 'sv_SE',
  today: 'I dag',
  now: 'Nu',
  backToToday: 'Till idag',
  ok: 'OK',
  clear: 'Av<PERSON><PERSON><PERSON>',
  week: '<PERSON>ecka',
  month: '<PERSON><PERSON>na<PERSON>',
  year: 'År',
  timeSelect: 'Välj tidpunkt',
  dateSelect: 'Välj datum',
  monthSelect: 'Välj månad',
  yearSelect: 'Välj år',
  decadeSelect: 'Välj årtionde',

  previousMonth: '<PERSON><PERSON><PERSON> måna<PERSON> (PageUp)',
  nextMonth: '<PERSON><PERSON><PERSON> månad (PageDown)',
  previousYear: '<PERSON><PERSON>reg år (Control + left)',
  nextYear: 'N<PERSON>sta år (Control + right)',
  previousDecade: 'Föreg årtionde',
  nextDecade: 'Nästa årtionde',
  previousCentury: 'Föreg århundrade',
  nextCentury: 'Nästa århundrade',
};

export default locale;
