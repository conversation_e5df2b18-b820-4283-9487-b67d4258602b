import { commonLocale } from './common';
import type { Locale } from '../interface';

const locale: Locale = {
  ...commonLocale,
  locale: 'az_AZ',
  today: 'Bugün',
  now: 'İndi',
  backToToday: 'Bug<PERSON>n<PERSON> qayıt',
  ok: 'Təsdiq',
  clear: 'Təmizlə',
  week: 'Həftə',
  month: 'Ay',
  year: 'İl',
  timeSelect: 'vaxtı seç',
  dateSelect: 'tarixi seç',
  weekSelect: 'Həftə seç',
  monthSelect: 'Ay seç',
  yearSelect: 'il seç',
  decadeSelect: 'Onillik seçin',
  previousMonth: 'Əvvəlki ay (PageUp)',
  nextMonth: 'Növbəti ay (PageDown)',
  previousYear: '<PERSON><PERSON><PERSON> il (Control + left)',
  nextYear: 'Növ<PERSON><PERSON>ti il (Control + right)',
  previousDecade: '<PERSON><PERSON><PERSON> onillik',
  nextDecade: '<PERSON>ö<PERSON><PERSON><PERSON>ti onillik',
  previousCentury: '<PERSON><PERSON><PERSON> əsr',
  nextCentury: '<PERSON>övbəti əsr',
};

export default locale;
