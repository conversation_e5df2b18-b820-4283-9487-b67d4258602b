import { commonLocale } from './common';
import type { Locale } from '../interface';

const locale: Locale = {
  ...commonLocale,
  locale: 'ro_RO',
  today: 'Azi',
  now: 'Acum',
  backToToday: '<PERSON><PERSON><PERSON> la azi',
  ok: 'OK',
  clear: 'Ș<PERSON>ge',
  week: 'Săpt<PERSON>mân<PERSON>',
  month: 'Lun<PERSON>',
  year: 'An',
  timeSelect: 'selectează timpul',
  dateSelect: 'selectează data',
  weekSelect: 'Alege o săptămână',
  monthSelect: 'Alege o lună',
  yearSelect: 'Alege un an',
  decadeSelect: 'Alege un deceniu',

  previousMonth: 'Luna anterioară (PageUp)',
  nextMonth: '<PERSON> urmă<PERSON>are (PageDown)',
  previousYear: 'Anul anterior (Control + stânga)',
  nextYear: 'Anul următor (Control + dreapta)',
  previousDecade: 'Deceniul anterior',
  nextDecade: 'Deceniul următor',
  previousCentury: 'Secolul anterior',
  nextCentury: 'Secolul următor',
};

export default locale;
