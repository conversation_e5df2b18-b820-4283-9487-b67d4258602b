import { commonLocale } from './common';
import type { Locale } from '../interface';

const locale: Locale = {
  ...commonLocale,
  locale: 'cs_CZ',
  today: 'Dnes',
  now: 'Nyn<PERSON>',
  backToToday: '<PERSON><PERSON><PERSON><PERSON> na dnešek',
  ok: 'OK',
  clear: 'Vymazat',
  week: 'Týden',
  month: 'M<PERSON>s<PERSON><PERSON>',
  year: 'Rok',
  timeSelect: 'Vybrat čas',
  dateSelect: 'Vybrat datum',
  monthSelect: 'Vyberte měsíc',
  yearSelect: 'Vyberte rok',
  decadeSelect: '<PERSON>yberte dek<PERSON>',

  previousMonth: '<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> (PageUp)',
  nextMonth: '<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> (PageDown)',
  previousYear: '<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> rok (Control + left)',
  nextYear: '<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> rok (Control + right)',
  previousDecade: '<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> de<PERSON>',
  nextDecade: 'Následuj<PERSON><PERSON><PERSON> de<PERSON>',
  previousCentury: '<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> století',
  nextCentury: '<PERSON>ásleduj<PERSON><PERSON><PERSON> století',
};

export default locale;
