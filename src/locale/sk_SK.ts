import { commonLocale } from './common';
import type { Locale } from '../interface';

const locale: Locale = {
  ...commonLocale,
  locale: 'sk_SK',
  today: 'Dnes',
  now: '<PERSON>raz',
  backToToday: 'Späť na dnes',
  ok: 'OK',
  clear: 'Vymaz<PERSON><PERSON>',
  week: 'Týžde<PERSON>',
  month: 'Mesiac',
  year: 'Rok',
  timeSelect: 'Vybrať čas',
  dateSelect: 'Vybra<PERSON> dátum',
  monthSelect: 'Vybra<PERSON> mesiac',
  yearSelect: 'V<PERSON>bra<PERSON> rok',
  decadeSelect: 'Vybrať dekádu',

  previousMonth: 'Pred<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> mesiac (PageUp)',
  nextMonth: 'Nasledu<PERSON><PERSON><PERSON> mesiac (PageDown)',
  previousYear: 'Predchádza<PERSON><PERSON><PERSON> rok (Control + left)',
  nextYear: 'Nasledu<PERSON><PERSON><PERSON> rok (Control + right)',
  previousDecade: 'Predchádzaj<PERSON><PERSON> de<PERSON>',
  nextDecade: 'Nasleduj<PERSON>ca de<PERSON>á<PERSON>',
  previousCentury: 'Predchádzaj<PERSON>ce storočie',
  nextCentury: 'Nasledujúce storočie',
};

export default locale;
