import { commonLocale } from './common';
import type { Locale } from '../interface';

const locale: Locale = {
  ...commonLocale,
  locale: 'fr_BE',
  today: "Aujourd'hui",
  now: 'Maintenant',
  backToToday: "Aujourd'hui",
  ok: 'OK',
  clear: '<PERSON><PERSON><PERSON>blir',
  week: '<PERSON><PERSON><PERSON>',
  month: '<PERSON><PERSON>',
  year: '<PERSON><PERSON>',
  timeSelect: "Sélectionner l'heure",
  dateSelect: "Sélectionner l'heure",
  monthSelect: 'Choisissez un mois',
  yearSelect: 'Choisissez une année',
  decadeSelect: 'Choisissez une décennie',

  previousMonth: '<PERSON><PERSON> précédent (PageUp)',
  nextMonth: '<PERSON><PERSON> suivant (PageDown)',
  previousYear: '<PERSON><PERSON> précédente (Ctrl + gauche)',
  nextYear: '<PERSON><PERSON> prochaine (Ctrl + droite)',
  previousDecade: 'Décennie précédente',
  nextDecade: '<PERSON><PERSON>ce<PERSON><PERSON> suivante',
  previousCentury: 'Siècle précédent',
  nextCentury: 'Siècle suivant',
};

export default locale;
