import { commonLocale } from './common';
import type { Locale } from '../interface';

const locale: Locale = {
  ...commonLocale,
  locale: 'fr_CA',
  today: "Aujourd'hui",
  now: 'Maintenant',
  backToToday: "Aujourd'hui",
  ok: 'OK',
  clear: '<PERSON><PERSON><PERSON>blir',
  week: '<PERSON><PERSON><PERSON>',
  month: '<PERSON><PERSON>',
  year: 'Ann<PERSON>',
  timeSelect: "Sélectionner l'heure",
  dateSelect: 'Sélectionner la date',
  monthSelect: 'Choisissez un mois',
  yearSelect: 'Choisissez une année',
  decadeSelect: 'Choisissez une décennie',

  dayFormat: 'DD',

  previousMonth: '<PERSON><PERSON> précédent (PageUp)',
  nextMonth: '<PERSON><PERSON> suivant (PageDown)',
  previousYear: '<PERSON><PERSON> précédente (Ctrl + gauche)',
  nextYear: '<PERSON><PERSON> prochaine (Ctrl + droite)',
  previousDecade: '<PERSON>écenn<PERSON> précédente',
  nextDecade: '<PERSON><PERSON><PERSON><PERSON><PERSON> suivante',
  previousCentury: 'Siècle précédent',
  nextCentury: 'Siècle suivant',
};

export default locale;
