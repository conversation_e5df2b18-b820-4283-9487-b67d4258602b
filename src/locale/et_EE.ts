import { commonLocale } from './common';
import type { Locale } from '../interface';

const locale: Locale = {
  ...commonLocale,
  locale: 'et_EE',
  today: 'Täna',
  now: '<PERSON><PERSON><PERSON>',
  backToToday: 'Tagasi tänase juurde',
  ok: 'OK',
  clear: '<PERSON><PERSON><PERSON><PERSON>',
  week: 'Nädal',
  month: 'Kuu',
  year: 'Aasta',
  timeSelect: 'Vali aeg',
  dateSelect: '<PERSON><PERSON> kuup<PERSON>ev',
  monthSelect: 'Vali kuu',
  yearSelect: 'Vali aasta',
  decadeSelect: 'Vali dekaad',

  previousMonth: '<PERSON><PERSON><PERSON> kuu (PageUp)',
  nextMonth: '<PERSON><PERSON><PERSON><PERSON> kuu (PageDown)',
  previousYear: '<PERSON><PERSON><PERSON> aasta (Control + left)',
  nextYear: '<PERSON><PERSON><PERSON><PERSON> aasta (Control + right)',
  previousDecade: '<PERSON><PERSON><PERSON> dekaad',
  nextDecade: '<PERSON><PERSON>rg<PERSON> dekaad',
  previousCentury: '<PERSON><PERSON><PERSON> sajand',
  nextCentury: '<PERSON><PERSON><PERSON><PERSON> sajand',
};

export default locale;
