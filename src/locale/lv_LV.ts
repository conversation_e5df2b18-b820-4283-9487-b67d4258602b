import { commonLocale } from './common';
import type { Locale } from '../interface';

const locale: Locale = {
  ...commonLocale,
  locale: 'lv_LV',
  today: 'Šodien',
  now: 'Tagad',
  backToToday: 'Atpakaļ pie šodienas',
  ok: 'OK',
  clear: 'Skaidrs',
  week: '<PERSON><PERSON><PERSON><PERSON>',
  month: 'Mēnes<PERSON>',
  year: 'Gads',
  timeSelect: 'Izvēlieties laiku',
  dateSelect: 'Izvēlieties datumu',
  monthSelect: 'Izvēlieties mēnesi',
  yearSelect: 'Izvēlieties gadu',
  decadeSelect: 'Izvēlieties desmit gadus',

  previousMonth: '<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> mēnesis (PageUp)',
  nextMonth: '<PERSON><PERSON><PERSON>mmēnes (PageDown)',
  previousYear: '<PERSON>g<PERSON><PERSON><PERSON><PERSON> gads (Control + left)',
  nextYear: 'Nākamgad (Control + right)',
  previousDecade: '<PERSON>ēd<PERSON>j<PERSON> desmitgadē',
  nextDecade: 'Nāka<PERSON><PERSON> desmitgade',
  previousCentury: 'Pagāju<PERSON><PERSON><PERSON> gadsimtā',
  nextCentury: 'Nākamajā gadsimtā',
};

export default locale;
