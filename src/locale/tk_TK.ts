import { commonLocale } from './common';
import type { Locale } from '../interface';

const locale: Locale = {
  ...commonLocale,
  locale: 'tk_TK',
  today: '<PERSON>ugün',
  now: '<PERSON><PERSON>wa<PERSON>',
  backToToday: '<PERSON><PERSON><PERSON><PERSON> gaýt',
  ok: '<PERSON><PERSON><PERSON><PERSON>',
  clear: 'Ara<PERSON><PERSON>',
  month: 'A<PERSON>',
  week: 'Hep<PERSON>',
  year: 'Ýyl',
  timeSelect: 'Wagt saýla',
  dateSelect: '<PERSON><PERSON><PERSON> saýla',
  monthSelect: 'Aý saýla',
  yearSelect: 'Ýyl saýla',
  decadeSelect: 'On ýyllygy saýla',

  previousMonth: 'Öň<PERSON> aý (PageUp)',
  nextMonth: 'Soňky aý (PageDown)',
  previousYear: 'Öňki ýyl (Control + çep)',
  nextYear: 'Soňky ýyl (Control + sag)',
  previousDecade: 'Öňki on ýyl',
  nextDecade: 'Soňky on ýyl',
  previousCentury: '<PERSON>ň<PERSON> asyr',
  nextCentury: 'Soňky asyr',
};

export default locale;
