import { commonLocale } from './common';
import type { Locale } from '../interface';

const locale: Locale = {
  ...commonLocale,
  locale: 'tr_TR',
  today: 'Bugün',
  now: '<PERSON>im<PERSON>',
  backToToday: '<PERSON><PERSON><PERSON><PERSON>',
  ok: 'Tamam',
  clear: 'Te<PERSON><PERSON>',
  week: 'Hafta',
  month: 'Ay',
  year: 'Yıl',
  timeSelect: '<PERSON><PERSON> Seç',
  dateSelect: '<PERSON><PERSON><PERSON>',
  monthSelect: '<PERSON><PERSON>ç',
  yearSelect: 'Yıl Seç',
  decadeSelect: 'On Yıl Seç',

  previousMonth: '<PERSON><PERSON><PERSON> A<PERSON> (PageUp)',
  nextMonth: '<PERSON><PERSON><PERSON> (PageDown)',
  previousYear: '<PERSON><PERSON><PERSON> (Control + Sol)',
  nextYear: '<PERSON><PERSON><PERSON> (Control + Sağ)',
  previousDecade: 'Önceki On Yıl',
  nextDecade: 'Sonraki On Yıl',
  previousCentury: '<PERSON>nce<PERSON> Yüzyıl',
  nextCentury: '<PERSON><PERSON><PERSON>',
  shortWeekDays: ['<PERSON>', 'Pzt', 'Sal', '<PERSON><PERSON>', 'Per', 'Cum', 'Cmt'],
  shortMonths: ['Oca', '<PERSON>ub', 'Mar', 'Nis', 'May', 'Haz', 'Tem', 'Ağu', 'Eyl', 'Eki', 'Kas', 'Ara'],
};

export default locale;
