import { commonLocale } from './common';
import type { Locale } from '../interface';

const locale: Locale = {
  ...commonLocale,
  locale: 'de_DE',
  today: 'He<PERSON>',
  now: '<PERSON>zt',
  backToToday: '<PERSON><PERSON><PERSON> zu Heute',
  ok: 'OK',
  clear: '<PERSON><PERSON><PERSON>set<PERSON>',
  week: 'Woche',
  month: '<PERSON><PERSON>',
  year: 'Jahr',
  timeSelect: 'Zeit wählen',
  dateSelect: 'Da<PERSON> wählen',
  monthSelect: 'Wähle einen Monat',
  yearSelect: 'Wähle ein Jahr',
  decadeSelect: 'Wähle ein Jahrzehnt',

  previousMonth: '<PERSON><PERSON><PERSON><PERSON> (PageUp)',
  nextMonth: '<PERSON><PERSON><PERSON><PERSON> (PageDown)',
  previousYear: 'Vorheriges Jahr (Ctrl + left)',
  nextYear: '<PERSON><PERSON>chs<PERSON> Jahr (Ctrl + right)',
  previousDecade: 'Vorheriges Jahrzehnt',
  nextDecade: 'Nächstes Jahrzehnt',
  previousCentury: 'Vorheriges Jahrhundert',
  nextCentury: 'Nächstes Jahrhundert',
};

export default locale;
