import { commonLocale } from './common';
import type { Locale } from '../interface';

const locale: Locale = {
  ...commonLocale,
  locale: 'pt_PT',
  today: 'Hoje',
  now: 'Agora',
  backToToday: '<PERSON><PERSON>',
  ok: 'OK',
  clear: '<PERSON><PERSON>',
  week: 'Se<PERSON>',
  month: '<PERSON><PERSON><PERSON>',
  year: 'Ano',
  timeSelect: 'Selecionar hora',
  dateSelect: 'Selecionar data',
  monthSelect: 'Selecionar mês',
  yearSelect: 'Selecionar ano',
  decadeSelect: 'Selecionar década',

  previousMonth: 'M<PERSON>s anterior (PageUp)',
  nextMonth: '<PERSON><PERSON><PERSON> se<PERSON> (PageDown)',
  previousYear: 'Ano anterior (Control + left)',
  nextYear: 'An<PERSON> seguinte (Control + right)',
  previousDecade: 'Década anterior',
  nextDecade: 'Década seguinte',
  previousCentury: 'Século anterior',
  nextCentury: 'Século seguinte',
  shortWeekDays: ['Dom', 'Seg', 'Ter', 'Qua', 'Qui', 'Sex', '<PERSON>áb'],
  shortMonths: ['Jan', 'Fev', 'Mar', 'Abr', '<PERSON>', 'Jun', 'Jul', 'Ago', 'Set', 'Out', 'Nov', 'Dez'],
};

export default locale;
