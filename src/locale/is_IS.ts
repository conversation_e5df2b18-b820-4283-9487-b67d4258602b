import { commonLocale } from './common';
import type { Locale } from '../interface';

const locale: Locale = {
  ...commonLocale,
  locale: 'is_IS',
  today: 'Í dag',
  now: 'Núna',
  backToToday: 'Til baka til dagsins í dag',
  ok: 'Í lagi',
  clear: '<PERSON><PERSON><PERSON><PERSON>',
  week: 'Vika',
  month: '<PERSON><PERSON>uður',
  year: 'Ár',
  timeSelect: 'Velja tíma',
  dateSelect: 'Velja dag',
  monthSelect: 'Velja mánuð',
  yearSelect: 'Velja ár',
  decadeSelect: 'Velja áratug',

  previousMonth: '<PERSON><PERSON><PERSON> mánuður (PageUp)',
  nextMonth: '<PERSON><PERSON><PERSON><PERSON> mánuður (PageDown)',
  previousYear: '<PERSON><PERSON><PERSON> ár (Control + left)',
  nextYear: '<PERSON>æ<PERSON> ár (Control + right)',
  previousDecade: '<PERSON><PERSON>ri áratugur',
  nextDecade: '<PERSON>æst<PERSON> áratugur',
  previousCentury: '<PERSON><PERSON><PERSON> öld',
  nextCentury: 'Næsta öld',
};

export default locale;
