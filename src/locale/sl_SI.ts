import { commonLocale } from './common';
import type { Locale } from '../interface';

const locale: Locale = {
  ...commonLocale,
  locale: 'sl_SI',
  today: '<PERSON><PERSON>',
  now: 'Trenutno',
  backToToday: '<PERSON><PERSON><PERSON> na danes',
  ok: 'V redu',
  clear: '<PERSON><PERSON><PERSON><PERSON>',
  week: '<PERSON><PERSON>',
  month: 'Mesec',
  year: 'Leto',
  timeSelect: 'Izberite čas',
  dateSelect: 'Izberite datum',
  monthSelect: 'Izberite mesec',
  yearSelect: 'Izberite leto',
  decadeSelect: 'Izberite desetletje',

  previousMonth: 'Pre<PERSON><PERSON><PERSON> mesec (PageUp)',
  nextMonth: 'Na<PERSON><PERSON><PERSON> mesec (PageDown)',
  previousYear: 'Prej<PERSON><PERSON><PERSON> leto (Control + left)',
  nextYear: 'Nasle<PERSON><PERSON> leto (Control + right)',
  previousDecade: 'Prejšnje desetletje',
  nextDecade: 'Naslednje desetletje',
  previousCentury: 'Pre<PERSON><PERSON><PERSON><PERSON> stoletje',
  nextCentury: 'Naslednje stoletje',
};

export default locale;
