import { commonLocale } from './common';
import type { Locale } from '../interface';

const locale: Locale = {
  ...commonLocale,
  locale: 'da_DK',
  today: 'I dag',
  now: 'Nu',
  backToToday: '<PERSON><PERSON> til i dag',
  ok: 'OK',
  clear: 'Ryd',
  week: 'Uge',
  month: 'Må<PERSON>',
  year: 'År',
  timeSelect: 'Vælg tidspunkt',
  dateSelect: 'Vælg dato',
  monthSelect: 'Vælg måned',
  yearSelect: 'Vælg år',
  decadeSelect: 'Vælg årti',

  previousMonth: '<PERSON><PERSON><PERSON> måned (Page Up)',
  nextMonth: '<PERSON><PERSON><PERSON> måned (Page Down)',
  previousYear: '<PERSON>rige år (Ctrl-venstre pil)',
  nextYear: 'Næste år (Ctrl-højre pil)',
  previousDecade: 'Forrige årti',
  nextDecade: 'Næste årti',
  previousCentury: 'Forrige århundrede',
  nextCentury: 'Næste århundrede',
};

export default locale;
