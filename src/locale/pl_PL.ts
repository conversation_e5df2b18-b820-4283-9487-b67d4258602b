import { commonLocale } from './common';
import type { Locale } from '../interface';

const locale: Locale = {
  ...commonLocale,
  locale: 'pl_PL',
  today: 'Dzisiaj',
  now: 'Teraz',
  backToToday: 'Ustaw dzisiaj',
  ok: 'OK',
  clear: '<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>',
  week: 'Tydzień',
  month: 'Mi<PERSON>ą<PERSON>',
  year: 'Rok',
  timeSelect: 'Ustaw czas',
  dateSelect: 'Ustaw datę',
  monthSelect: 'Wybierz miesiąc',
  yearSelect: 'Wybierz rok',
  decadeSelect: 'Wybierz dekadę',

  previousMonth: '<PERSON><PERSON><PERSON><PERSON> mi<PERSON> (PageUp)',
  nextMonth: 'Nast<PERSON><PERSON>ny miesiąc (PageDown)',
  previousYear: 'Ostatni rok (Ctrl + left)',
  nextYear: 'Następny rok (Ctrl + right)',
  previousDecade: 'Ostatnia dekada',
  nextDecade: 'Nast<PERSON><PERSON>na dekada',
  previousCentury: '<PERSON>sta<PERSON><PERSON> wiek',
  nextCentury: 'Nast<PERSON>pny wiek',
};

export default locale;
