import { commonLocale } from './common';
import type { Locale } from '../interface';

const locale: Locale = {
  ...commonLocale,
  locale: 'ku',
  today: 'Îro',
  now: '<PERSON>ha',
  backToToday: '<PERSON><PERSON><PERSON> îro',
  ok: '<PERSON><PERSON><PERSON>',
  clear: '<PERSON><PERSON>j bike',
  week: '<PERSON><PERSON><PERSON><PERSON>',
  month: 'Me<PERSON>',
  year: 'Sal',
  timeSelect: '<PERSON><PERSON><PERSON> hilbijêre',
  dateSelect: '<PERSON><PERSON><PERSON> hilbijêre',
  monthSelect: '<PERSON>h hilbijêre',
  yearSelect: '<PERSON> hilbijêre',
  decadeSelect: 'Dehsal hilbijêre',

  previousMonth: '<PERSON><PERSON> peş (PageUp))',
  nextMonth: '<PERSON><PERSON> paş (PageDown)',
  previousYear: '<PERSON><PERSON> peş (Control + şep)',
  nextYear: '<PERSON>a paş (Control + rast)',
  previousDecade: 'Dehsalen peş',
  nextDecade: 'Dehsalen paş',
  previousCentury: 'Sedsalen peş',
  nextCentury: 'Sedsalen paş',
};

export default locale;
