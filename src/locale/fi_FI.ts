import { commonLocale } from './common';
import type { Locale } from '../interface';

const locale: Locale = {
  ...commonLocale,
  locale: 'fi_FI',
  today: 'Tänään',
  now: 'Nyt',
  backToToday: '<PERSON>ä<PERSON><PERSON> päivä',
  ok: 'OK',
  clear: '<PERSON><PERSON><PERSON>nn<PERSON>',
  week: 'Viikko',
  month: '<PERSON>ukaus<PERSON>',
  year: 'Vuosi',
  timeSelect: 'Valise aika',
  dateSelect: '<PERSON><PERSON><PERSON> päivä',
  monthSelect: 'Valitse kuukausi',
  yearSelect: 'Valitse vuosi',
  decadeSelect: 'Valitse vuosikymmen',

  previousMonth: '<PERSON><PERSON><PERSON> kuukausi (PageUp)',
  nextMonth: '<PERSON><PERSON><PERSON> kuukausi (PageDown)',
  previousYear: '<PERSON><PERSON><PERSON> vuosi (Control + left)',
  nextYear: '<PERSON><PERSON><PERSON> vuosi (Control + right)',
  previousDecade: 'Edellinen vuosikymmen',
  nextDecade: 'Seuraava vuosikymmen',
  previousCentury: '<PERSON><PERSON><PERSON> vuosisata',
  nextCentury: 'Seuraava vuosisata',
};

export default locale;
