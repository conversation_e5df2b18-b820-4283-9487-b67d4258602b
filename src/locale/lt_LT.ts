import { commonLocale } from './common';
import type { Locale } from '../interface';

const locale: Locale = {
  ...commonLocale,
  locale: 'lt_LT',
  today: 'Šiandien',
  now: 'Da<PERSON>',
  backToToday: '<PERSON><PERSON><PERSON> šiandien',
  ok: '<PERSON><PERSON><PERSON>',
  clear: '<PERSON><PERSON><PERSON><PERSON><PERSON>',
  week: '<PERSON>vait<PERSON>',
  month: '<PERSON><PERSON><PERSON><PERSON>',
  year: '<PERSON>ai',
  timeSelect: 'Pasirinkti laiką',
  dateSelect: 'Pasirinkti datą',
  weekSelect: 'Pasirinkti savaitę',
  monthSelect: 'Pasirinkti mėnesį',
  yearSelect: 'Pasirinkti metus',
  decadeSelect: 'Pasirinkti dešimtmetį',

  dayFormat: 'DD',

  previousMonth: '<PERSON>uv<PERSON><PERSON> mėnes<PERSON> (PageUp)',
  nextMonth: '<PERSON><PERSON> mėnesis (PageDown)',
  previousYear: 'Buv<PERSON> metai (Control + left)',
  nextYear: '<PERSON>i metai (Control + right)',
  previousDecade: '<PERSON>uvę<PERSON> dešimtmet<PERSON>',
  nextDecade: 'Kitas dešimtmetis',
  previousCentury: '<PERSON>uvę<PERSON> am<PERSON>',
  nextCentury: 'Kitas amžius',
};

export default locale;
