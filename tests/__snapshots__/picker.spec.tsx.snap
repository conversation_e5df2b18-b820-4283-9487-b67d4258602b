// Jest Snapshot v1, https://goo.gl/fbAQLP

exports[`Picker.Basic icon 1`] = `
<div
  class="rc-picker-input"
>
  <input
    aria-invalid="false"
    autocomplete="off"
    size="12"
    value="1990-09-03"
  />
  <span
    class="rc-picker-suffix"
  >
    <span
      class="suffix-icon"
    />
  </span>
  <span
    class="rc-picker-clear"
    role="button"
  >
    <span
      class="suffix-icon"
    />
  </span>
</div>
`;

exports[`Picker.Basic inputRender 1`] = `
<div
  class="rc-picker-input"
>
  <input
    aria-invalid="false"
    autocomplete="off"
    data-customize="yes"
    size="12"
    value=""
  />
</div>
`;

exports[`Picker.Basic panelRender 1`] = `
<div
  class="rc-picker"
>
  <div
    class="rc-picker-input"
  >
    <input
      aria-invalid="false"
      autocomplete="off"
      size="12"
      value=""
    />
  </div>
</div>
`;

exports[`Picker.Basic pass data- & aria- & role 1`] = `
<div>
  <div
    class="rc-picker"
  >
    <div
      class="rc-picker-input"
    >
      <input
        aria-invalid="false"
        aria-label="3334"
        autocomplete="off"
        data-test="233"
        role="search"
        size="12"
        value=""
      />
    </div>
  </div>
</div>
`;

exports[`Picker.Basic should render correctly in rtl 1`] = `
<div>
  <div
    class="rc-picker rc-picker-rtl"
  >
    <div
      class="rc-picker-input"
    >
      <input
        aria-invalid="false"
        autocomplete="off"
        size="12"
        value=""
      />
    </div>
  </div>
</div>
`;
